defmodule Drops.Relations.QueryAPISpec do
  use ExUnit.Case, async: false

  setup do
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Drops.TestRepo)
  end

  describe "query API functions" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "provides delegating functions for Ecto.Repo operations" do
      defmodule Test.Relations.QueryUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Test that all expected functions exist
      query_functions = [
        {:get, 2},
        {:get!, 2},
        {:get_by, 2},
        {:get_by!, 2},
        {:all, 1},
        {:one, 1},
        {:one!, 1},
        {:insert, 1},
        {:insert!, 1},
        {:update, 2},
        {:update!, 2},
        {:delete, 1},
        {:delete!, 1},
        {:count, 1},
        {:first, 1},
        {:last, 1}
      ]

      for {func, arity} <- query_functions do
        assert function_exported?(Test.Relations.QueryUsers, func, arity),
               "Function #{func}/#{arity} should be exported"
      end
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "query functions work with actual data" do
      defmodule Test.Relations.QueryUsersData do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Clean up any existing data
      Drops.TestRepo.delete_all(Test.Relations.QueryUsersData.Schema)

      # Test count on empty table
      assert Test.Relations.QueryUsersData.count() == 0

      # Test all on empty table
      assert Test.Relations.QueryUsersData.all() == []

      # Insert a user using raw SQL first, then test query functions
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "INSERT INTO users (name, email) VALUES (?, ?)",
        ["Test User", "<EMAIL>"]
      )

      # Get the inserted user to test with
      [user] = Test.Relations.QueryUsersData.all()
      assert user.name == "Test User"

      # Test count after insert
      assert Test.Relations.QueryUsersData.count() == 1

      # Test get
      found_user = Test.Relations.QueryUsersData.get(user.id)
      assert found_user.name == "Test User"
      assert found_user.email == "<EMAIL>"

      # Test get_by
      found_by_email = Test.Relations.QueryUsersData.get_by(email: "<EMAIL>")
      assert found_by_email.id == user.id

      # Test all
      all_users = Test.Relations.QueryUsersData.all()
      assert length(all_users) == 1
      assert hd(all_users).id == user.id

      # Test update
      {:ok, updated_user} =
        Test.Relations.QueryUsersData.update(user, %{name: "Updated User"})

      assert updated_user.name == "Updated User"

      # Test delete
      {:ok, _deleted_user} = Test.Relations.QueryUsersData.delete(updated_user)
      assert Test.Relations.QueryUsersData.count() == 0
    end
  end

  describe "index-based finders" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "generates get_by_{field} functions for indexed fields" do
      # Create a table with indices for testing
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        """
        CREATE TABLE IF NOT EXISTS test_indexed_users_query (
          id INTEGER PRIMARY KEY,
          email TEXT UNIQUE,
          username TEXT,
          status TEXT
        )
        """,
        []
      )

      # Create indices
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "CREATE INDEX IF NOT EXISTS idx_test_indexed_users_query_username ON test_indexed_users_query(username)",
        []
      )

      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "CREATE INDEX IF NOT EXISTS idx_test_indexed_users_query_status ON test_indexed_users_query(status)",
        []
      )

      defmodule Test.Relations.IndexedUsersQuery do
        use Drops.Relation,
          repo: Drops.TestRepo,
          name: "test_indexed_users_query",
          infer: true
      end

      # Test that index-based finder functions exist
      index_functions = [:get_by_email, :get_by_username, :get_by_status]

      for func <- index_functions do
        assert function_exported?(Test.Relations.IndexedUsersQuery, func, 1),
               "Function #{func}/1 should be exported"
      end

      # Test actual usage
      # Insert test data
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "INSERT OR REPLACE INTO test_indexed_users_query (email, username, status) VALUES (?, ?, ?)",
        ["<EMAIL>", "testuser", "active"]
      )

      # Test the finders
      user_by_email = Test.Relations.IndexedUsersQuery.get_by_email("<EMAIL>")
      assert user_by_email != nil
      assert user_by_email.username == "testuser"

      user_by_username = Test.Relations.IndexedUsersQuery.get_by_username("testuser")
      assert user_by_username != nil
      assert user_by_username.email == "<EMAIL>"

      user_by_status = Test.Relations.IndexedUsersQuery.get_by_status("active")
      assert user_by_status != nil
      assert user_by_status.username == "testuser"
    end
  end

  describe "nested Schema module" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "generates proper Ecto.Schema module" do
      defmodule Test.Relations.SchemaUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Test that the Schema module exists and behaves like an Ecto.Schema
      assert Code.ensure_loaded?(Test.Relations.SchemaUsers.Schema)

      # Test Ecto.Schema functions
      assert Test.Relations.SchemaUsers.Schema.__schema__(:source) == "users"
      assert :id in Test.Relations.SchemaUsers.Schema.__schema__(:fields)
      assert :name in Test.Relations.SchemaUsers.Schema.__schema__(:fields)
      assert :email in Test.Relations.SchemaUsers.Schema.__schema__(:fields)

      # Test that we can create structs (using apply to avoid compile-time issues)
      schema_module = Test.Relations.SchemaUsers.Schema
      user_struct = struct(schema_module, %{name: "Test", email: "<EMAIL>"})
      assert user_struct.name == "Test"
      assert user_struct.email == "<EMAIL>"

      # Test that the struct works with Ecto.Repo functions
      {:ok, inserted_user} = Drops.TestRepo.insert(user_struct)
      assert inserted_user.name == "Test"
      assert inserted_user.email == "<EMAIL>"
    end
  end

  describe "parent module schema() function" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "provides access to Drops.Relation.Schema" do
      defmodule Test.Relations.SchemaAccessUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      schema = Test.Relations.SchemaAccessUsers.schema()
      assert schema.__struct__ == Drops.Relation.Schema
      assert schema.source == "users"
      assert length(schema.fields) > 0

      # Check that fields contain expected field structs
      field_names = Enum.map(schema.fields, & &1.name)
      assert :id in field_names
      assert :name in field_names
      assert :email in field_names
    end
  end
end
