defmodule Drops.Relation do
  defmacro __using__(opts) do
    quote do
      import Drops.Relation

      @before_compile Drops.Relation

      @opts unquote(opts)
    end
  end

  defmacro associations(do: block) do
    quote do
      @associations unquote(Macro.escape(block))
    end
  end

  defmacro __before_compile__(env) do
    relation = env.module

    opts = Module.get_attribute(relation, :opts)
    repo = opts[:repo]
    name = opts[:name]

    {ecto_schema_ast, drops_schema} =
      Drops.Relation.SchemaCache.get_or_infer_schema(repo, name, fn ->
        Drops.Relation.Inference.infer_schema(relation, name, repo)
      end)

    # Generate the nested Schema module
    schema_module_ast = generate_schema_module(relation, ecto_schema_ast)

    # Generate query API functions
    query_api_ast = generate_query_api(opts, drops_schema)

    quote do
      require unquote(repo)

      # Define the nested Schema module
      unquote(schema_module_ast)

      # Store configuration as module attributes
      @opts unquote(Macro.escape(opts))
      @schema unquote(Macro.escape(drops_schema))

      # Generate query API functions
      unquote_splicing(query_api_ast)

      @doc """
      Returns the inferred Drops.Relation.Schema for this relation.

      The schema contains comprehensive metadata including primary keys,
      foreign keys, field information, indices, and virtual fields.

      ## Examples

          iex> MyRelation.schema()
          %Drops.Relation.Schema{
            source: "my_table",
            primary_key: %Drops.Relation.Schema.PrimaryKey{fields: [:id]},
            foreign_keys: [...],
            fields: [...],
            indices: %Drops.Relation.Schema.Indices{...},
            virtual_fields: [...]
          }
      """
      @spec schema() :: Drops.Relation.Schema.t()
      def schema do
        @schema
      end
    end
  end

  # Generates the nested Schema module AST
  defp generate_schema_module(relation, ecto_schema_ast) do
    schema_module_name = Module.concat(relation, Schema)

    quote do
      defmodule unquote(schema_module_name) do
        use Ecto.Schema

        unquote(ecto_schema_ast)
      end
    end
  end

  # Generates query API functions
  defp generate_query_api(opts, drops_schema) do
    repo = opts[:repo]

    # Basic Ecto.Repo functions
    basic_functions = [
      generate_get_function(repo),
      generate_get_bang_function(repo),
      generate_get_by_function(repo),
      generate_get_by_bang_function(repo),
      generate_all_function(repo),
      generate_one_function(repo),
      generate_one_bang_function(repo),
      generate_insert_function(repo),
      generate_insert_bang_function(repo),
      generate_update_function(repo),
      generate_update_bang_function(repo),
      generate_delete_function(repo),
      generate_delete_bang_function(repo),
      generate_count_function(repo),
      generate_first_function(repo),
      generate_last_function(repo)
    ]

    # Index-based finder functions
    index_functions = generate_index_based_finders(repo, drops_schema)

    basic_functions ++ index_functions
  end

  # Basic Ecto.Repo function generators
  defp generate_get_function(repo) do
    quote do
      @doc "Gets a single record by primary key"
      def get(id, opts \\ []) do
        unquote(repo).get(__MODULE__.Schema, id, opts)
      end
    end
  end

  defp generate_get_bang_function(repo) do
    quote do
      @doc "Gets a single record by primary key, raises if not found"
      def get!(id, opts \\ []) do
        unquote(repo).get!(__MODULE__.Schema, id, opts)
      end
    end
  end

  defp generate_get_by_function(repo) do
    quote do
      @doc "Gets a single record by the given clauses"
      def get_by(clauses, opts \\ []) do
        unquote(repo).get_by(__MODULE__.Schema, clauses, opts)
      end
    end
  end

  defp generate_get_by_bang_function(repo) do
    quote do
      @doc "Gets a single record by the given clauses, raises if not found"
      def get_by!(clauses, opts \\ []) do
        unquote(repo).get_by!(__MODULE__.Schema, clauses, opts)
      end
    end
  end

  defp generate_all_function(repo) do
    quote do
      @doc "Fetches all records matching the given query"
      def all(queryable \\ __MODULE__.Schema, opts \\ []) do
        unquote(repo).all(queryable, opts)
      end
    end
  end

  defp generate_one_function(repo) do
    quote do
      @doc "Fetches a single result from the query"
      def one(queryable, opts \\ []) do
        unquote(repo).one(queryable, opts)
      end
    end
  end

  defp generate_one_bang_function(repo) do
    quote do
      @doc "Fetches a single result from the query, raises if not found or more than one"
      def one!(queryable, opts \\ []) do
        unquote(repo).one!(queryable, opts)
      end
    end
  end

  defp generate_insert_function(repo) do
    quote do
      @doc "Inserts a struct or changeset"
      def insert(struct_or_changeset, opts \\ []) do
        unquote(repo).insert(struct_or_changeset, opts)
      end
    end
  end

  defp generate_insert_bang_function(repo) do
    quote do
      @doc "Inserts a struct or changeset, raises on error"
      def insert!(struct_or_changeset, opts \\ []) do
        unquote(repo).insert!(struct_or_changeset, opts)
      end
    end
  end

  defp generate_update_function(repo) do
    quote do
      @doc "Updates a changeset"
      def update(changeset, opts \\ []) do
        unquote(repo).update(changeset, opts)
      end
    end
  end

  defp generate_update_bang_function(repo) do
    quote do
      @doc "Updates a changeset, raises on error"
      def update!(changeset, opts \\ []) do
        unquote(repo).update!(changeset, opts)
      end
    end
  end

  defp generate_delete_function(repo) do
    quote do
      @doc "Deletes a struct"
      def delete(struct, opts \\ []) do
        unquote(repo).delete(struct, opts)
      end
    end
  end

  defp generate_delete_bang_function(repo) do
    quote do
      @doc "Deletes a struct, raises on error"
      def delete!(struct, opts \\ []) do
        unquote(repo).delete!(struct, opts)
      end
    end
  end

  defp generate_count_function(repo) do
    quote do
      @doc "Returns the count of records"
      def count(queryable \\ __MODULE__.Schema) do
        unquote(repo).aggregate(queryable, :count)
      end
    end
  end

  defp generate_first_function(repo) do
    quote do
      @doc "Returns the first record"
      def first(queryable \\ __MODULE__.Schema, opts \\ []) do
        unquote(repo).one(Ecto.Query.first(queryable), opts)
      end
    end
  end

  defp generate_last_function(repo) do
    quote do
      @doc "Returns the last record"
      def last(queryable \\ __MODULE__.Schema, opts \\ []) do
        unquote(repo).one(Ecto.Query.last(queryable), opts)
      end
    end
  end

  # Generates index-based finder functions
  defp generate_index_based_finders(repo, drops_schema) do
    alias Drops.Relation.Schema.Index

    # Get all indices from the schema
    indices = drops_schema.indices.indices

    # Generate finders for single-field indices
    single_field_finders =
      indices
      |> Enum.reject(&Index.composite?/1)
      |> Enum.map(&generate_single_field_finder(repo, &1))

    # For now, skip composite indices (can be added later)
    single_field_finders
  end

  defp generate_single_field_finder(repo, index) do
    alias Drops.Relation.Schema.Index
    [field] = Index.field_names(index)
    function_name = String.to_atom("get_by_#{field}")

    quote do
      @doc unquote("Gets a record by #{field}")
      def unquote(function_name)(value, opts \\ []) do
        unquote(repo).get_by(__MODULE__.Schema, [{unquote(field), value}], opts)
      end
    end
  end

  defmodule Inference do
    alias Drops.Relation.SQL

    def infer_schema(relation, name, repo) do
      # Use the unified schema inference implementation
      drops_schema = SQL.Inference.infer_from_table(name, repo)

      # Generate Ecto schema fields from the inferred schema
      field_definitions = generate_field_definitions_from_schema(drops_schema)

      # Get optional Ecto associations definitions AST
      association_definitions = Module.get_attribute(relation, :associations, [])

      # Create the Ecto schema AST
      ecto_schema =
        quote do
          schema unquote(name) do
            (unquote_splicing(field_definitions))

            unquote(association_definitions)
          end
        end

      {ecto_schema, drops_schema}
    end

    # Generates Ecto field definitions from a Drops.Relation.Schema
    defp generate_field_definitions_from_schema(schema) do
      schema.fields
      |> Enum.reject(fn field ->
        # Skip timestamp fields and primary key named 'id' (Ecto adds this automatically)
        field.name in [:inserted_at, :updated_at, :id]
      end)
      |> Enum.map(fn field ->
        # Regular field (primary key 'id' is handled automatically by Ecto)
        quote do
          field(unquote(field.name), unquote(field.ecto_type))
        end
      end)
    end
  end
end
